using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Xml;
using System.Text;
using System.Linq;
using LitJson;

public class CollectCharsTools
{
    private const string TABLE_JSON_PATH = "../Excels/json";
    private const string UI_PROJECT_PATH = "../UIProject/assets";
    private const string OUTPUT_FONT_PATH = "_MyGame/RawRes/Fonts/RHRC.txt";

    [MenuItem("Tools/Collect Chars", priority = 20)]
    public static void CollectChars()
    {
        var chars = new HashSet<char>();

        // 添加基础字符
        string basicChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789" +
                          ",.?!:;\"'()[]{}+-*/=_@#$%&|<>～，。？！：；（）【】《》、—…￥";
        foreach (char c in basicChars)
        {
            chars.Add(c);
        }

        // 收集 JSON 文件中的字符
        string jsonPath = Path.Combine(Application.dataPath, TABLE_JSON_PATH);
        if (Directory.Exists(jsonPath))
        {
            CollectFromJsonFiles(jsonPath, chars);
        }

        // 收集 XML 文件中的字符
        string xmlPath = Path.Combine(Application.dataPath, UI_PROJECT_PATH);
        if (Directory.Exists(xmlPath))
        {
            CollectFromXMLFiles(xmlPath, chars);
        }

        // 输出字符到文件
        string outputPath = Path.Combine(Application.dataPath, OUTPUT_FONT_PATH);
        Directory.CreateDirectory(Path.GetDirectoryName(outputPath));
        File.WriteAllText(outputPath, new string(chars.ToArray()), Encoding.UTF8);

        Debug.Log($"字符收集完成，共 {chars.Count} 个字符，已保存到: {outputPath}");
        AssetDatabase.Refresh();
    }

    private static void CollectFromJsonFiles(string path, HashSet<char> chars)
    {
        foreach (string file in Directory.GetFiles(path, "*.json", SearchOption.AllDirectories))
        {
            try
            {
                string jsonContent = File.ReadAllText(file);
                JsonData jsonData = JsonMapper.ToObject(jsonContent);
                CollectFromJsonData(jsonData, chars);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"处理JSON文件失败 {file}: {e.Message}");
            }
        }
    }

    private static void CollectFromJsonData(JsonData jsonData, HashSet<char> chars)
    {
        if (jsonData == null) return;

        if (jsonData.IsString)
        {
            string value = jsonData.ToString();
            if (!string.IsNullOrEmpty(value))
            {
                foreach (char c in value)
                {
                    chars.Add(c);
                }
            }
        }
        else if (jsonData.IsObject)
        {
            foreach (string key in jsonData.Keys)
            {
                CollectFromJsonData(jsonData[key], chars);
            }
        }
        else if (jsonData.IsArray)
        {
            for (int i = 0; i < jsonData.Count; i++)
            {
                CollectFromJsonData(jsonData[i], chars);
            }
        }
    }

    private static void CollectFromXMLFiles(string path, HashSet<char> chars)
    {
        foreach (string file in Directory.GetFiles(path, "*.xml", SearchOption.AllDirectories))
        {
            try
            {
                var doc = new XmlDocument();
                doc.Load(file);
                CollectCharsFromNode(doc.DocumentElement, chars);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"处理XML文件失败 {file}: {e.Message}");
            }
        }
    }

    private static void CollectCharsFromNode(XmlNode node, HashSet<char> chars)
    {
        if (node == null) return;

        // 收集节点文本
        if (!string.IsNullOrEmpty(node.InnerText))
        {
            foreach (char c in node.InnerText)
            {
                chars.Add(c);
            }
        }

        // 收集属性值
        if (node.Attributes != null)
        {
            foreach (XmlAttribute attr in node.Attributes)
            {
                if (!string.IsNullOrEmpty(attr.Value))
                {
                    foreach (char c in attr.Value)
                    {
                        chars.Add(c);
                    }
                }
            }
        }

        // 递归处理子节点
        foreach (XmlNode child in node.ChildNodes)
        {
            CollectCharsFromNode(child, chars);
        }
    }
}