using UnityEngine;
using UnityEditor;
using TMPro;
using System.IO;
using System.Text;

public class CreateTMPAsset
{
    private const string SOURCE_FOLDER = "Assets/_MyGame/RawRes/Fonts";
    private static string OUTPUT_FOLDER1 = "Assets/_MyGame/Resources/Bundles/Fonts";
    private static string OUTPUT_FOLDER2 = "Assets/_MyGame/Bundles/Fonts";
    private const int ATLAS_WIDTH = 1024;
    private const int ATLAS_HEIGHT = 2048;
    private const int SAMPLING_POINT_SIZE = 40;
    private const float BOLD_WEIGHT = 0.1f;
    private const int ATLAS_PADDING = 9;
    private const bool ATLAS_Enable_Multi_Atlas = true;

    [MenuItem("Tools/Create TMP Assets", priority = 22)]
    public static void CreateFontAssets()
    {
        // 确保输出目录存在
        if (!Directory.Exists(OUTPUT_FOLDER1))
        {
            OUTPUT_FOLDER1 = OUTPUT_FOLDER2;
            if (!Directory.Exists(OUTPUT_FOLDER1))
            {
                Directory.CreateDirectory(OUTPUT_FOLDER1);
            }
        }

        // 获取所有ttf文件
        string[] fontFiles = Directory.GetFiles(SOURCE_FOLDER, "*.ttf");

        foreach (string fontFile in fontFiles)
        {
            string fileName = Path.GetFileNameWithoutExtension(fontFile);
            string textFile = Path.Combine(SOURCE_FOLDER, fileName + ".txt");

            // 检查对应的txt文件是否存在
            if (!File.Exists(textFile))
            {
                Debug.LogWarning($"Text file not found for font: {fileName}: {textFile}");
                continue;
            }

            // 读取字符集
            string characters = File.ReadAllText(textFile, Encoding.UTF8);

            // 检查OUTPUT_FOLDER1是否已存在fontAsset
            string sdfAssetPath = Path.Combine(OUTPUT_FOLDER1, $"{fileName} SDF.asset");
            TMP_FontAsset fontAsset = AssetDatabase.LoadAssetAtPath<TMP_FontAsset>(sdfAssetPath);

            if (fontAsset != null)
            {
                Debug.Log($"Found existing font asset: {sdfAssetPath}, using it directly");
            }
            else
            {
                // 加载字体文件
                Font sourceFont = AssetDatabase.LoadAssetAtPath<Font>(Path.Join(SOURCE_FOLDER, fileName + ".ttf"));
                if (sourceFont == null)
                {
                    Debug.LogError($"Failed to load font: {fontFile}");
                    continue;
                }

                // 设置TMP字体生成设置
                fontAsset = TMP_FontAsset.CreateFontAsset(
                    font: sourceFont,
                    atlasPopulationMode: AtlasPopulationMode.Dynamic,
                    renderMode: UnityEngine.TextCore.LowLevel.GlyphRenderMode.SDFAA,
                    samplingPointSize: SAMPLING_POINT_SIZE,
                    atlasPadding: ATLAS_PADDING,
                    atlasWidth: ATLAS_WIDTH,
                    atlasHeight: ATLAS_HEIGHT,
                    enableMultiAtlasSupport: ATLAS_Enable_Multi_Atlas
                    );

                if (fontAsset == null)
                {
                    Debug.LogError($"Failed to create TMP font asset for: {fileName}");
                    continue;
                }
            }

            // 设置字体属性
            fontAsset.boldStyle = BOLD_WEIGHT;

            // 添加字符到字体图集
            fontAsset.TryAddCharacters(characters);

            // 如果是新创建的资产，需要保存
            if (!File.Exists(sdfAssetPath))
            {
                // 保存资源
                AssetDatabase.CreateAsset(fontAsset, sdfAssetPath);

                // 保存所有 Atlas Textures
                if (fontAsset.atlasTextures != null)
                {
                    for (int i = 0; i < fontAsset.atlasTextures.Length; i++)
                    {
                        var texture = fontAsset.atlasTextures[i];
                        if (texture != null)
                        {
                            texture.name = $"{fileName} Atlas {i + 1}";
                            AssetDatabase.AddObjectToAsset(texture, fontAsset);
                        }
                    }
                }

                // 保存字体材质
                Material material = new(Shader.Find("TextMeshPro/Distance Field"));
                AssetDatabase.AddObjectToAsset(material, fontAsset);
                fontAsset.material = material;
            }

            AssetDatabase.DeleteAsset(Path.Join(OUTPUT_FOLDER1, fileName + ".ttf"));

            Debug.Log($"Create success: {fileName}");
            EditorUtility.SetDirty(fontAsset);
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        Debug.Log("TMP Font Assets creation completed!");
    }
}