using System;
using System.Diagnostics;
using System.IO;
using UnityEditor;
using UnityEngine;

public class TTFMini
{
    private const string SOURCE_FOLDER = "Assets/_MyGame/RawRes/Fonts";
    private static string OUTPUT_FOLDER1 = "Assets/_MyGame/Resources/Bundles/Fonts";
    private static string OUTPUT_FOLDER2 = "Assets/_MyGame/Bundles/Fonts";
    private const string MINI_FONT_TOOL_PATH = "Tools/MiniFont";

    private static Action onCreateFontAssets;

    [MenuItem("Tools/TTFMini", priority = 21)]
    public static void CreateFontAssets()
    {
        CreateFontAssets(null);
    }
    public static void CreateFontAssets(Action callback)
    {
        onCreateFontAssets = callback;
        // 确保输出目录存在
        if (!Directory.Exists(OUTPUT_FOLDER1))
        {
            OUTPUT_FOLDER1 = OUTPUT_FOLDER2;
            if (!Directory.Exists(OUTPUT_FOLDER1))
            {
                Directory.CreateDirectory(OUTPUT_FOLDER1);
            }
        }

        // 获取所有ttf文件
        string[] fontFiles = Directory.GetFiles(SOURCE_FOLDER, "*.ttf");
        string projectPath = Path.GetDirectoryName(Application.dataPath);
        string toolPath = Path.Combine(projectPath, MINI_FONT_TOOL_PATH);
        string outputPath = Path.Combine(projectPath, OUTPUT_FOLDER1);

        var curCount = 0;
        foreach (string fontFile in fontFiles)
        {
            string fileName = Path.GetFileNameWithoutExtension(fontFile);
            string textFile = Path.Combine(SOURCE_FOLDER, fileName + ".txt");

            // 检查对应的txt文件是否存在
            if (!File.Exists(textFile))
            {
                UnityEngine.Debug.LogWarning($"Text file not found for font: {fileName}: {textFile}");
                continue;
            }

            // 调用MiniFont工具处理单个字体文件
            RunMiniFontTool(toolPath, fontFile, textFile, outputPath, () =>
            {
                curCount++;
                if (curCount == fontFiles.Length)
                {
                    callback?.Invoke();
                }
            });
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        UnityEngine.Debug.Log("字体资源创建完成");
    }

    private static void RunMiniFontTool(string toolPath, string fontFile, string textFile, string outputPath, Action callback)
    {
        // 转换为完整的文件系统路径
        string fullFontPath = Path.Combine(Path.GetDirectoryName(Application.dataPath), fontFile);
        string fullTextPath = Path.Combine(Path.GetDirectoryName(Application.dataPath), textFile);

        ProcessStartInfo startInfo = new ProcessStartInfo();
        startInfo.FileName = "cmd.exe";
        startInfo.Arguments = $"/c cd /d \"{toolPath}\" && node miniFont.js \"{fullFontPath}\" \"{fullTextPath}\" \"{outputPath}\"";
        startInfo.UseShellExecute = false;
        startInfo.CreateNoWindow = false;
        startInfo.RedirectStandardOutput = true;
        startInfo.RedirectStandardError = true;

        UnityEngine.Debug.Log($"Processing font: {Path.GetFileName(fontFile)}");
        UnityEngine.Debug.Log($"Font path: {fullFontPath}");
        UnityEngine.Debug.Log($"Text path: {fullTextPath}");

        using (Process process = Process.Start(startInfo))
        {
            string output = process.StandardOutput.ReadToEnd();
            string error = process.StandardError.ReadToEnd();
            process.WaitForExit();

            if (!string.IsNullOrEmpty(output))
                UnityEngine.Debug.Log("MiniFont output: " + output);

            if (!string.IsNullOrEmpty(error))
                UnityEngine.Debug.LogError("MiniFont error: " + error);

            if (process.ExitCode != 0)
                UnityEngine.Debug.LogError($"Font processing failed: {Path.GetFileName(fontFile)}");

            callback?.Invoke();
        }
    }
}