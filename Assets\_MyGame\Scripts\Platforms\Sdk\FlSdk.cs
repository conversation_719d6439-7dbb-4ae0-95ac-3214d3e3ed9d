using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using LitJson;
using Newtonsoft.Json;
using UnityEngine;

public class FlSdk : MonoBehaviour
{
    private static Action _OnAdReward;
    private static Action _OnAdFail;
    private static Action _OnShareSuccess;
    private static Action _OnShareFail;

    // #if UNITY_EDITOR || !UNITY_WEBGL
    private static string FlSdkInit()
    {
        Debug.Log("[FlSdk] FlSdkInit");
        return "";
    }
    public static void ActiveReport()
    {
        Debug.Log("[FlSdk] ActiveReport");
    }
    private static void SendEvent(string eventName, string data, string opt)
    {
        Debug.Log("[FlSdk] SendEvent:" + eventName + "  data:" + data + "  opt:" + opt);
    }
    private static void ShowRewardVideoAd(string positionTag) { }
    private static void ShareAppMessage(string positionTag) { }
    private static string GetAbTest()
    {
        Debug.Log("[FlSdk] GetAbTest");
        return "";
    }
    private static void SetCurrentLevel(int level)
    {
        Debug.Log($"[FlSdk] SetCurrentLevel:{level}");
    }
    private static void VideoExposure(int count)
    {
        Debug.Log($"[FlSdk] VideoExposure:{count}");
    }
    // #else
    //     [DllImport("__Internal")]
    //     private static extern string FlSdkInit();
    //     [DllImport("__Internal")]
    //     public static extern void ActiveReport();
    //     [DllImport("__Internal")]
    //     private static extern void SendEvent(string eventName, string data, string opt);
    //     [DllImport("__Internal")]
    //     private static extern void ShowRewardVideoAd(string positionTag);
    //     [DllImport("__Internal")]
    //     private static extern void ShareAppMessage(string positionTag);
    //     [DllImport("__Internal")]
    //     private static extern string GetAbTest();
    //     [DllImport("__Internal")]
    //     private static extern void SetCurrentLevel(int level);
    //     [DllImport("__Internal")]
    //     private static extern void VideoExposure(int count);
    // #endif

    public static FlSdk Inst;
    private void Awake()
    {
        Inst = this;
    }

    public void Init()
    {
        // waitFlsdkInit = StartCoroutine(InitFlSdkDone());
        // FlSdkInit();
        OnFlSdkInit(0);
    }

    private Coroutine waitFlsdkInit;
    IEnumerator InitFlSdkDone()
    {
        yield return new WaitForSeconds(3);
        Debug.Log("[FlSdk] InitFlSdkDone timeout");
        OnFlSdkInit(0);
    }

    public Action<int> OnFlSdkInitAction;
    public void OnFlSdkInit(int abTest)
    {
        // StopCoroutine(waitFlsdkInit);
        OnFlSdkInitAction?.Invoke(abTest);
    }

    public string GetFlSdkAbTest()
    {
        return GetAbTest();
    }

    public void ShowVideoAd(string positionTag, Action OnAdReward, Action OnFail)
    {
        _OnAdReward = OnAdReward;
        _OnAdFail = OnFail;
        ShowRewardVideoAd(positionTag);
        // Debug.Log("[FlSdk] ShowRewardVideoAd");
    }
    public void OnRewardVideoAdSuccess()
    {
        _OnAdReward?.Invoke();
        IncrementChapterVideoNum();
        // Debug.Log("[FlSdk] OnRewardVideoAdSuccess");
    }
    public void OnRewardVideoAdCancel()
    {
        _OnAdFail?.Invoke();
        // Debug.Log("[FlSdk] OnRewardVideoAdCancel");
    }
    public void OnRewardVideoAdFail()
    {
        _OnAdFail?.Invoke();
        // Debug.Log("[FlSdk] OnRewardVideoAdFail");
    }

    public void Share(string positionTag, Action OnShareSuccess, Action OnShareFail)
    {
        _OnShareSuccess = OnShareSuccess;
        _OnShareFail = OnShareFail;
        ShareAppMessage(positionTag);
        // Debug.Log("[FlSdk] ShareAppMessage");
    }

    public void OnShareSuccess()
    {
        _OnShareSuccess?.Invoke();
        // Debug.Log("[FlSdk] OnShareSuccess");
    }
    public void OnShareFail()
    {
        _OnShareFail?.Invoke();
    }
    public void ReportLoadTime(bool isFirst, float runtime)
    {
        var data = new Flload_time()
        {
            is_first = isFirst,
            runtime = runtime
        };
        _SendEvent("load_time", JsonUtility.ToJson(data));
    }

    public void ReportGuideComplete(int guide_id)
    {
        var data = new Flguide_complete()
        {
            guide_id = guide_id
        };
        _SendEvent("guide_complete", JsonUtility.ToJson(data));
    }

    public void ReportCurrentLevel(int level)
    {
        SetCurrentLevel(level);
    }
    public void ReportVideoExposure(int count)
    {
        VideoExposure(count);
    }

    /// <summary>
    /// 体力上报
    /// </summary>
    /// <param name="add_num">购买体力数量</param>
    /// <param name="isByVideo">是否是广告购买</param>
    /// <param name="last_num">剩余体力数量</param>
    public void ReportBuyStamina(int add_num, bool isByVideo, int last_num)
    {
        var data = new Flbuy_stamina()
        {
            add_num = add_num,
            but_type = isByVideo ? "广告购买" : "金币购买",
            last_num = last_num
        };
        _SendEvent("buy_stamina", JsonUtility.ToJson(data));
    }

    private int gamePlayedTime;
    /// <summary>
    /// 关卡开始时上报
    /// </summary>
    public void ReportChapterPlay()
    {
        gamePlayedTime = 0;

        ReportCurrentLevel(GameGlobal.Level);
        var data = new Flchapter_play()
        {
            chapter_level = GameGlobal.Level,
            prompt_num = GameGlobal.ItemTurnCount,
            shuffle_num = GameGlobal.ItemShuffleCount,
            magnet_num = GameGlobal.ItemMagnetCount,
            move_out_num = GameGlobal.ItemBulbCount
        };
        _SendEvent("chapter_play", JsonUtility.ToJson(data));
    }

    private void _SendEvent(string eventName, string data, string opt = "")
    {
        SendEvent(eventName, data, opt);
    }

    public void UpdateGamePlayedTime()
    {
        gamePlayedTime++;
    }

    public void ReportChapterEnd(bool isWin)
    {
        var playTime = gamePlayedTime;

        var data = new Flchapter_end()
        {
            chapter_level = GameGlobal.Level,
            prompt_num = GameGlobal.ItemTurnCount,
            shuffle_num = GameGlobal.ItemShuffleCount,
            magnet_num = GameGlobal.ItemMagnetCount,
            move_out_num = GameGlobal.ItemBulbCount,

            chapter_tips_num = turnCount,
            chapter_tips_video_num = turnVideoCount,

            chapter_shuffle_num = shuffleCount,
            chapter_shuffle_video_num = shuffleVideoCount,

            chapter_magnet_num = magnetCount,
            chapter_magnet_video_num = magnetVideoCount,

            chapter_move_out_num = blobCount,
            chapter_move_out_video_num = blobVideoCount,

            chapter_video_num = chapterVideoNum,

            is_first = GameGlobal.IsFirstTimePlayLevel(GameGlobal.Level),
            is_cleared = isWin,
            time = playTime,
            limit_duration = Mathf.FloorToInt(GameGlobal.GameDurationTime)
        };
        _SendEvent("chapter_end", JsonUtility.ToJson(data));
    }

    public void ReportChapterLevel()
    {
        UserSet("current_chapter_level", GameGlobal.Level);
    }
    public void ReportCoinAndHeart()
    {
        UserSet("current_coin", GameGlobal.Gold);
        UserSet("current_stamina", GameGlobal.CurHeartCount);
    }
    public void ReportAbTest(string type)
    {
        //A用户走原本逻辑，B、C用户走新逻辑
        UserSetOnce("ab_test", type);
    }

    public void UserSet(string key, int value)
    {
        var data = new JsonData()
        {
            [key] = value
        };
        _SendEvent("", data.ToJson(), "userSet");
    }
    public void UserSetOnce(string key, string value)
    {
        var data = new JsonData()
        {
            [key] = value
        };
        _SendEvent("", data.ToJson(), "userSetOnce");
    }
    public void ResetTempData()
    {
        blobCount = 0;
        shuffleCount = 0;
        turnCount = 0;
        magnetCount = 0;
        blobVideoCount = 0;
        shuffleVideoCount = 0;
        turnVideoCount = 0;
        magnetVideoCount = 0;
        chapterVideoNum = 0;
    }
    public void IncrementChapterVideoNum()
    {
        chapterVideoNum++;
    }

    private int blobCount;
    private int blobVideoCount;
    private int shuffleCount;
    private int shuffleVideoCount;
    private int turnCount;
    private int turnVideoCount;
    private int magnetCount;
    private int magnetVideoCount;
    private int chapterVideoNum;

    public void ReportUseItemCount(int itemId, bool isUseByVideo)
    {
        if (isUseByVideo)
        {
            switch (itemId)
            {
                case ItemId.ItemBulb:
                    blobVideoCount++;
                    break;
                case ItemId.ItemShuffle:
                    shuffleVideoCount++;
                    break;
                case ItemId.ItemTurn:
                    turnVideoCount++;
                    break;
                case ItemId.ItemMagnet:
                    magnetVideoCount++;
                    break;
            }
        }
        else
        {
            switch (itemId)
            {
                case ItemId.ItemBulb:
                    blobCount++;
                    break;
                case ItemId.ItemShuffle:
                    shuffleCount++;
                    break;
                case ItemId.ItemTurn:
                    turnCount++;
                    break;
                case ItemId.ItemMagnet:
                    magnetCount++;
                    break;
            }
        }
    }
}