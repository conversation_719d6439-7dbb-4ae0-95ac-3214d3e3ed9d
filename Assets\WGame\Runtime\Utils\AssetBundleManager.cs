﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;

#if YOOASSET
using YooAsset;
#endif

public class AssetBundleManager
{
    public static bool IsInitialize;

    private static string bundleDir;
    private static string ResourceDir;
#if YOOASSET
    private static Dictionary<string, SceneOperationHandle> sceneHandlerDic = new Dictionary<string, SceneOperationHandle>();
    private static ResourcePackage package;
    public static void Initialize(string bundleRoot, ResourcePackage yooPackage)
    {
        IsInitialize = true;
        bundleDir = bundleRoot;
        package = yooPackage;
    }
#endif

    public static void Initialize(string bundleRoot)
    {
        IsInitialize = true;
        ResourceDir = bundleRoot;
    }
    public static string GetVersion()
    {
#if YOOASSET
        if (package == null)
            return string.Empty;
        return package.GetPackageVersion();
#endif
        return string.Empty;
    }
    public static string GetVersion2()
    {
        var ver = GetVersion();
        if (!string.IsNullOrEmpty(ver) && ver.Contains("_"))
        {
            var verAry = ver.Split('_');
            if (verAry.Length > 0)
            {
                return verAry[1];
            }
        }
        return string.Empty;
    }
    public static void LoadFont(string path, Action<Font> callBack)
    {
        LoadObject(path, (obj) =>
        {
            if (obj != null)
            {
                callBack.Invoke((obj as Font));
            }
            else
            {
                callBack.Invoke(null);
            }
        });
    }

    public static void LoadAudio(string path, Action<AudioClip> callBack)
    {
        LoadObject(path, (obj) =>
        {
            if (obj != null)
            {
                callBack.Invoke((obj as AudioClip));
            }
            else
            {
                callBack.Invoke(null);
            }
        });
    }

    public static void LoadBytes(string path, Action<byte[]> callBack)
    {
        LoadObject(path, (obj) =>
        {
            if (obj != null)
            {
                callBack.Invoke((obj as TextAsset).bytes);
            }
            else
            {
                callBack.Invoke(null);
            }
        });
    }

    public static void LoadTexture(string path, Action<Texture> callBack)
    {
        LoadObject(path, (obj) =>
        {
            if (obj != null)
            {
                callBack.Invoke(obj as Texture);
            }
            else
            {
                callBack.Invoke(null);
            }
        });
    }

    public static void LoadText(string path, Action<string> callBack)
    {
        LoadObject(path, (obj) =>
        {
            if (obj != null)
            {
                callBack.Invoke((obj as TextAsset).text);
            }
            else
            {
                callBack.Invoke(null);
            }
        });
    }

    public static void LoadAnimator(string path, Action<RuntimeAnimatorController> callBack)
    {
        LoadObject(path, (obj) =>
        {
            if (obj != null)
            {
                callBack.Invoke((obj as RuntimeAnimatorController));
            }
            else
            {
                callBack.Invoke(null);
            }
        });
    }

    public static void LoadPrefab(string path, Action<GameObject> callBack = null)
    {
        LoadObject(path, (obj) =>
        {
            if (obj != null)
            {
                callBack?.Invoke((obj as GameObject));
            }
            else
            {
                callBack?.Invoke(null);
            }
        });
    }

    public static void LoadAssets(string tag, Action<IList<object>> onCompleted, Action<object> stepCallback = null)
    {
        if (UseYooAsset(string.Empty))
        {
            var assets = package.GetAssetInfos(tag);
            var count = assets.Length;
            var loaded = 0;
            List<object> result = new List<object>();
            for (int i = 0; i < count; i++)
            {
                var path = assets[i].AssetPath.Replace(bundleDir, string.Empty);
                LoadObject(path, (data) =>
                {
                    loaded++;
                    result.Add(data);
                    stepCallback?.Invoke(data);
                    if (loaded >= count)
                    {
                        onCompleted.Invoke(result);
                    }
                });
            }
            if (count == 0)
            {
                onCompleted.Invoke(result);
            }
        }
    }

    public static void LoadSprite(string path, Action<Sprite> callBack)
    {
        string srcPath;
        if (UseYooAsset(path))
        {
            srcPath = bundleDir + path;
            var handle = package.LoadAssetAsync<UnityEngine.Sprite>(srcPath);
            handle.Completed += (data) =>
            {
                callBack.Invoke(data.AssetObject as Sprite);
            };
        }
        else
        {
            srcPath = ResourceDir + path;
            var res = Resources.Load<UnityEngine.Sprite>(srcPath);
            callBack.Invoke(res);
        }
    }

    //private static Dictionary<string, AsyncOperationHandle> handleCache = new Dictionary<string, AsyncOperationHandle>();
    public static void LoadObject(string path, Action<object> callBack)
    {
        string srcPath;
        if (UseYooAsset(path))
        {
            srcPath = bundleDir + path;
            var handle = package.LoadAssetAsync<UnityEngine.Object>(srcPath);
            handle.Completed += (data) =>
            {
                callBack.Invoke(data.AssetObject);
            };
        }
        else
        {
            srcPath = ResourceDir + path;
            var res = Resources.Load<UnityEngine.Object>(srcPath);
            callBack.Invoke(res);
        }
    }


    public static void LoadScene(string path, LoadSceneMode mode, Action callBack = null, bool suspendLoad = false)
    {
        string srcPath;
        if (UseYooAsset(path))
        {
            srcPath = bundleDir + path;
            var handle = package.LoadSceneAsync(srcPath, mode, suspendLoad);
            //只处理Additive，使用LoadScene会卸载所有旧Single场景
            if (mode == LoadSceneMode.Additive)
            {
                if (sceneHandlerDic.ContainsKey(srcPath))
                {
                    Debug.LogWarning("The previous scene was unload:" + srcPath);
                    UnloadScene(srcPath);
                }
                sceneHandlerDic[srcPath] = handle;
            }

            handle.Completed += (data) =>
            {
                var scene = data.SceneObject;
                callBack?.Invoke();
            };
        }
        else
        {
            srcPath = ResourceDir + path;
            // srcPath = "_MyGame/Resources/" + srcPath;
            SceneManager.LoadSceneAsync(srcPath, mode).completed += (res) =>
            {
                callBack?.Invoke();
            };
        }
    }

    public static void UnloadScene(string path)
    {
        string srcPath;
        if (UseYooAsset(path))
        {
            srcPath = bundleDir + path;
            sceneHandlerDic.TryGetValue(path, out SceneOperationHandle preHandle);
            if (preHandle != null)
            {
                preHandle.UnloadAsync();
                sceneHandlerDic.Remove(srcPath);
            }
        }
        else
        {
            // srcPath = "_MyGame/Resources/" + path;
            srcPath = ResourceDir + path;
            SceneManager.UnloadSceneAsync(srcPath);
        }
    }
#if YOOASSET
    public static T LoadAssetSync<T>(string path) where T : UnityEngine.Object
    {
        var srcPath = bundleDir + path;
        return (T)YooAssets.LoadAssetSync<T>(srcPath).AssetObject;
    }
    public static T LoadSubAssetSync<T>(string path, string spriteName) where T : UnityEngine.Object
    {
        var srcPath = bundleDir + path;
        return (T)YooAssets.LoadSubAssetsSync<T>(srcPath).GetSubAssetObject<T>(spriteName);
    }
#endif
    public static void Release(string path)
    {
        //if (string.IsNullOrEmpty(path)) return;

        //if (handleCache.TryGetValue(path, out var handle))
        //{
        //    if (handle.IsValid())
        //    {
        //        Addressables.Release(handle);
        //    }

        //    if (!handle.IsValid())
        //    {
        //        handleCache.Remove(path);
        //    }
        //}
    }

    public static bool UseYooAsset(string path)
    {
#if YOOASSET
        return path.Contains("DataSO");
#endif
        return false;
    }
}
